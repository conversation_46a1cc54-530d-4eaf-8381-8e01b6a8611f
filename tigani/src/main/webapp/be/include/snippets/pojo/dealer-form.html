<!-- Card -->
<div class="bg-white border border-gray-200 shadow-2xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <form>
        <div class="py-2 sm:py-4 px-2">
            <div class="p-2 sm:p-4 space-y-3">
                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Immagine profilo
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <input id="logo-offcanvas" name="logo" type="file" class="filepond w-[100px] cursor-pointer">                        
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->

                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="fullname" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Ragione Sociale <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <input id="fullname" placeholder="Ragione sociale" value="" type="text" required maxlength="100" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600">
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->  
                
                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="vatNumber" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Partita IVA <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <input id="vatNumber" name="vatNumber" placeholder="Partita IVA" value="" type="text" required maxlength="11" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600">
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->
                
                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="fiscalCode" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Codice fiscale
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <input id="fiscalCode" name="fiscalCode" placeholder="Partita IVA" value="" type="text" maxlength="11" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600">
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->
                
                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="sdi" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Codice SDI
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <input id="sdi" name="sdi" placeholder="Codice SDI" value="" type="text" maxlength="7" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600">
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->

                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="pec" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            PEC <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">                        
                        <input id="pec" name="pec" placeholder="PEC" value="" type="email" required maxlength="100" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600">                                                
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->
                
                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="email" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Email <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">                        
                        <input id="email" name="email" placeholder="Email" value="" type="email" required class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600">                                                
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->

                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="phoneNumber" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Telefono <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">
                        <div class="space-y-3">
                            <div class="grid grid-cols-12 gap-2">
                                <div class="col-span-4">
                                    <div class="relative">
                                        <select readonly data-hs-select='{
                                                "hasSearch": true,
                                                "placeholder": "Stato",
                                                "searchPlaceholder": "Cerca stato...",
                                                "searchNoResultText": "Nessun risultato",
                                                "searchClasses": "block w-full sm:text-sm border-gray-200 rounded-lg focus:border-blue-500 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder:text-neutral-400 py-1.5 sm:py-2 px-3",
                                                "searchWrapperClasses": "bg-white p-2 -mx-1 sticky top-0 dark:bg-neutral-900",
                                                "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span data-icon></span><span class=\"text-gray-800 dark:text-neutral-200\" data-title></span></button>",
                                                "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-1.5 sm:py-2 ps-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start sm:text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                                                "dropdownClasses": "max-h-64 p-1 pt-0 space-y-0.5 z-50 w-full overflow-hidden overflow-y-auto bg-white rounded-xl shadow-xl [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900",
                                                "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800",
                                                "optionTemplate": "<div><div class=\"flex items-center\"><div class=\"me-2\" data-icon></div><div class=\"text-gray-800 dark:text-neutral-200\" data-title></div></div></div>",
                                                "preventSearchFocus": false,
                                                "viewport": "#hs-pro-create-new-user"
                                                }' class="hidden">
                                            <option value="">Seleziona stato</option>

                                            <option value="+39" selected data-hs-select-option='{
                                                    "icon": "<img class=\"inline-block size-4 rounded-full\" src=\"{{ contextPath }}/img/lang/it.svg\" alt=\"Italy\" />"}'>
                                                + 39
                                            </option>                        
                                        </select>

                                        <div class="absolute top-1/2 end-2.5 -translate-y-1/2">
                                            <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="m7 15 5 5 5-5" />
                                                <path d="m7 9 5-5 5 5" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-span-8">
                                    <input id="phoneNumber" name="phoneNumber" type="text" placeholder="Telefono" value="" maxlength="100" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600">
                                </div>
                            </div>
                        </div>
                        
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->
                
                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="website" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Sito web
                        </label>
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-9">                                                                        
                        <div class="relative">
                            <input type="text" id="website" name="website" maxlength="100" class="py-1.5 sm:py-2 px-3 ps-16 block w-full border-gray-200 rounded-lg sm:text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="www.miosito.com">
                                <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none z-20 ps-4">
                                    <span class="text-sm text-gray-500 dark:text-neutral-500">https://</span>
                                </div>
                        </div>                                                    
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->                

                <!-- Grid -->
                <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                    <div class="sm:col-span-3">
                        <label for="fullAddress" class="sm:mt-2.5 inline-block text-sm font-medium text-gray-800 dark:text-neutral-200">
                            Indirizzo <span class="text-red-500">*</span>
                        </label>
                    </div>
                    <!-- End Col -->
                    
                    <div class="sm:col-span-9 space-y-3">
                        
                        <!-- Full address -->
                        <div>
                            <input id="fullAddress" name="fullAddress" type="text" placeholder="Indirizzo completo" value="" required maxlength="200" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600">
                            <p class="mt-1 text-xs text-gray-400 dark:text-neutral-400">Scrivi l'indirizzo completo i campi sottostanti si autocompileranno</p>
                        </div>                        
                        <!-- End Full address -->
                        
                        <hr class="border-gray-200">
                                                
                        <!-- Address + Street Number -->
                        <div class="space-y-3">                            
                            <div class="grid grid-cols-12 gap-2">
                                <div class="col-span-9">
                                    <input id="address" name="address" type="text" placeholder="Via\Piazza" value="" required maxlength="100" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600">
                                </div>
                                <div class="col-span-3">
                                    <input id="streetNumber" name="streetNumber" type="text" placeholder="Civico" value="" required maxlength="5" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600">
                                </div>
                            </div>                                                                                        
                        </div>
                        <!-- End Address + Street Number -->
                            
                        <!-- City -->
                        <div class="space-y-3">                                
                            <div class="relative">
                                <select id="city" name="city" data-hs-select='{
                                        "apiUrl": "{{ baseUrl }}{{ routes("BE_DATA_CITIES") }}",
                                        "apiQuery": "selected={{ curContact.city }}&q",
                                        "apiSearchMinLength": 1,
                                        "apiDataPart": "results",
                                        "apiFieldsMap": {
                                        "id": "id",
                                        "val": "id",
                                        "title": "text"
                                        },
                                        "apiSelectedValues": "{{ curContact.city }}",
                                        "hasSearch": true,
                                        "apiLoadMore": true,
                                        "placeholder": "Città",
                                        "searchPlaceholder": "Cerca città...",
                                        "searchNoResultText": "Nessun risultato",
                                        "searchClasses": "block w-full sm:text-sm border-gray-200 rounded-lg focus:border-blue-500 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder:text-neutral-400 py-1.5 sm:py-2 px-3",
                                        "searchWrapperClasses": "bg-white p-2 -mx-1 sticky top-0 dark:bg-neutral-900",                          
                                        "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span data-icon></span><span class=\"text-gray-800 dark:text-neutral-200\" data-title></span></button>",
                                        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-1.5 sm:py-2 ps-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start sm:text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                                        "dropdownClasses": "max-h-64 p-1 pt-0 space-y-0.5 z-50 w-full overflow-hidden overflow-y-auto bg-white rounded-xl shadow-xl [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900",
                                        "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800",
                                        "optionTemplate": "<div><div class=\"flex items-center\"><div class=\"me-2\" data-icon></div><div class=\"text-gray-800 dark:text-neutral-200\" data-title></div></div></div>"
                                        }' {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}disabled{% endif %}>                          
                                </select>
                                <div class="absolute top-1/2 end-2.5 -translate-y-1/2">
                                    <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m7 15 5 5 5-5" />
                                        <path d="m7 9 5-5 5 5" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <!-- End City -->
                        
                        <!-- Postal Code + Province Code -->
                        <div class="grid grid-cols-2 gap-x-3">                                
                            <div class="relative">                          
                                <select id="postalCode" name="postalCode" data-hs-select='{
                                        "apiUrl": "{{ baseUrl }}{{ routes("BE_DATA_PROVINCES") }}",
                                        "apiQuery": "selected={{ curContact.postalCode }}&q",
                                        "apiSearchMinLength": 1,
                                        "apiDataPart": "results",
                                        "apiFieldsMap": {
                                        "id": "id",
                                        "val": "id",
                                        "title": "text"
                                        },
                                        "apiSelectedValues": "{{ curContact.postalCode }}",
                                        "hasSearch": true,
                                        "apiLoadMore": true,
                                        "placeholder": "CAP",
                                        "searchPlaceholder": "Cerca CAP...",
                                        "searchNoResultText": "Nessun risultato",
                                        "searchClasses": "block w-full sm:text-sm border-gray-200 rounded-lg focus:border-blue-500 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder:text-neutral-400 py-1.5 sm:py-2 px-3",
                                        "searchWrapperClasses": "bg-white p-2 -mx-1 sticky top-0 dark:bg-neutral-900",                          
                                        "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span data-icon></span><span class=\"text-gray-800 dark:text-neutral-200\" data-title></span></button>",
                                        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-1.5 sm:py-2 ps-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start sm:text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                                        "dropdownClasses": "max-h-64 p-1 pt-0 space-y-0.5 z-50 w-full overflow-hidden overflow-y-auto bg-white rounded-xl shadow-xl [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900",
                                        "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800",
                                        "optionTemplate": "<div><div class=\"flex items-center\"><div class=\"me-2\" data-icon></div><div class=\"text-gray-800 dark:text-neutral-200\" data-title></div></div></div>"
                                        }' {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}disabled{% endif %}>
                                </select>
                                <div class="absolute top-1/2 end-2.5 -translate-y-1/2">
                                    <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m7 15 5 5 5-5" />
                                        <path d="m7 9 5-5 5 5" />
                                    </svg>
                                </div>
                            </div>
                            <div class="relative">                          
                                <select id="provinceCode" name="provinceCode" data-hs-select='{
                                        "apiUrl": "{{ baseUrl }}{{ routes("BE_DATA_PROVINCES") }}",
                                        "apiQuery": "selected={{ curContact.provinceCode }}&q",
                                        "apiSearchMinLength": 1,
                                        "apiDataPart": "results",
                                        "apiFieldsMap": {
                                        "id": "id",
                                        "val": "id",
                                        "title": "text"
                                        },
                                        "apiSelectedValues": "{{ curContact.provinceCode }}",
                                        "hasSearch": true,
                                        "apiLoadMore": true,
                                        "placeholder": "Provincia",
                                        "searchPlaceholder": "Cerca provincia...",
                                        "searchNoResultText": "Nessun risultato",
                                        "searchClasses": "block w-full sm:text-sm border-gray-200 rounded-lg focus:border-blue-500 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder:text-neutral-400 py-1.5 sm:py-2 px-3",
                                        "searchWrapperClasses": "bg-white p-2 -mx-1 sticky top-0 dark:bg-neutral-900",                          
                                        "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span data-icon></span><span class=\"text-gray-800 dark:text-neutral-200\" data-title></span></button>",
                                        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-1.5 sm:py-2 ps-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start sm:text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                                        "dropdownClasses": "max-h-64 p-1 pt-0 space-y-0.5 z-50 w-full overflow-hidden overflow-y-auto bg-white rounded-xl shadow-xl [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900",
                                        "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800",
                                        "optionTemplate": "<div><div class=\"flex items-center\"><div class=\"me-2\" data-icon></div><div class=\"text-gray-800 dark:text-neutral-200\" data-title></div></div></div>"
                                        }' {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}disabled{% endif %}>
                                </select>
                                <div class="absolute top-1/2 end-2.5 -translate-y-1/2">
                                    <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m7 15 5 5 5-5" />
                                        <path d="m7 9 5-5 5 5" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <!-- End Postal Code + Province Code -->
                        
                        <!-- Country -->
                        <div class="relative">
                            <select readonly data-hs-select='{
                                    "hasSearch": true,
                                    "placeholder": "Stato",
                                    "searchPlaceholder": "Cerca stato...",
                                    "searchNoResultText": "Nessun risultato",
                                    "searchClasses": "block w-full sm:text-sm border-gray-200 rounded-lg focus:border-blue-500 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder:text-neutral-400 py-1.5 sm:py-2 px-3",
                                    "searchWrapperClasses": "bg-white p-2 -mx-1 sticky top-0 dark:bg-neutral-900",
                                    "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span data-icon></span><span class=\"text-gray-800 dark:text-neutral-200\" data-title></span></button>",
                                    "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-1.5 sm:py-2 ps-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start sm:text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                                    "dropdownClasses": "max-h-64 p-1 pt-0 space-y-0.5 z-50 w-full overflow-hidden overflow-y-auto bg-white rounded-xl shadow-xl [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900",
                                    "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800",
                                    "optionTemplate": "<div><div class=\"flex items-center\"><div class=\"me-2\" data-icon></div><div class=\"text-gray-800 dark:text-neutral-200\" data-title></div></div></div>",
                                    "preventSearchFocus": false,
                                    "viewport": "#hs-pro-create-new-user"
                                    }' class="hidden">
                                <option value="">Seleziona stato</option>

                                <option value="IT" selected data-hs-select-option='{
                                        "icon": "<img class=\"inline-block size-4 rounded-full\" src=\"{{ contextPath }}/img/lang/it.svg\" alt=\"Italy\" />"}'>
                                    Italia
                                </option>                        
                            </select>

                            <div class="absolute top-1/2 end-2.5 -translate-y-1/2">
                                <svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m7 15 5 5 5-5" />
                                    <path d="m7 9 5-5 5 5" />
                                </svg>
                            </div>
                        </div>
                        <!-- End Country -->
                        
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Grid -->
            </div>
        </div>

        <!-- Footer -->
        <div class="p-6 pt-0 flex justify-end gap-x-2">
            <div class="w-full flex justify-end items-center gap-x-2">
                <a href="{{ routes('BE_DEALER_COLLECTION') }}" class="py-2 px-3 inline-flex justify-center items-center text-start bg-white border border-gray-200 text-gray-800 text-sm font-medium rounded-lg shadow-2xs align-middle hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                    Annulla
                </a>

                <button type="button" class="py-2 px-3 inline-flex justify-center items-center gap-x-2 text-start bg-blue-600 border border-blue-600 text-white text-sm font-medium rounded-lg shadow-2xs align-middle hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:ring-1 focus:ring-blue-300 dark:focus:ring-blue-500">
                    Aggiungi Rivenditore
                </button>
            </div>
        </div>
        <!-- End Footer -->
    </form>
</div>
<!-- End Card -->

{#
<!-- Dealer Form Content for Offcanvas -->
<script class="reload-script-on-load">
    addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
    addRoute('BE_DEALER_VIEW', '{{ routes("BE_DEALER_VIEW") }}');
    addRoute('BE_DEALER_SAVE', '{{ routes("BE_DEALER_SAVE") }}');
    addRoute('BE_DEALER_OPERATE', '{{ routes("BE_DEALER_OPERATE") }}');
    addVariables('imageId', '{{ curDealer.imageId }}');
</script>

<!-- Form Content with Sticky Footer -->
<div class="flex flex-col h-full">
    <!-- Scrollable Content Area -->
    <div class="flex-1 overflow-y-auto">
        <div class="p-4">
            {% set postUrl = routes('BE_DEALER_SAVE') %}
            {% if curDealer.id is not empty %}                
            {% set postUrl = routes('BE_DEALER_SAVE') + '?dealerId=' + curDealer.id %}
            {% endif %}

            <form id="dealer-edit-offcanvas" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                <!-- Profile Image -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Logo Dealer:
                    </label>
                    <div class="space-y-2 flex flex-col justify-center items-center text-center">
                        <input id="logo-offcanvas" name="logo" type="file" class="filepond filepond-avatar">
                            <p class="text-xs text-gray-500 dark:text-neutral-400">Formato immagine .jpg, .png o .svg.</p>
                    </div>
                </div>    

                <!-- Name -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Nome: <span class="text-red-500">*</span>
                    </label>
                    <input name="name" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Nome Dealer" value="{{ curDealer.name }}" required {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Company Name -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Ragione Sociale: <span class="text-red-500">*</span>
                    </label>
                    <input name="companyName" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Ragione Sociale" value="{{ curDealer.companyName }}" required {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Dealer Code -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Codice Dealer: <span class="text-red-500">*</span>
                    </label>
                    <input name="dealerCode" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Codice Dealer" value="{{ curDealer.dealerCode }}" required {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Email -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Email: <span class="text-red-500">*</span>
                    </label>
                    <input name="email" type="email" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" value="{{ curDealer.email }}" required {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Phone -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Telefono:
                    </label>
                    <input name="phoneNumber" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" value="{{ curDealer.phoneNumber }}" {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Address -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Indirizzo:
                    </label>
                    <textarea name="address" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" rows="3" {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>{{ curDealer.address }}</textarea>
                </div>

                <!-- VAT Number -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Partita IVA:
                    </label>
                    <input name="vatNumber" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" value="{{ curDealer.vatNumber }}" {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Notes -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Note:
                    </label>
                    <textarea name="notes" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" rows="3" {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}readonly{% endif %}>{{ curDealer.notes }}</textarea>
                </div>

                <!-- Status -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Stato: <span class="text-red-500">*</span>
                    </label>
                    <select name="status" class="py-2 px-3 pe-9 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:ring-neutral-600" data-hs-select='{
                            "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span class=\"me-2\" data-icon></span><span class=\"text-gray-800 dark:text-neutral-200\" data-title></span></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-neutral-600",
                            "dropdownClasses": "mt-2 max-h-72 pb-1 px-1 space-y-0.5 z-20 w-full bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800"
                            }' {% if not user.hasPermission('DEALER_MANAGEMENT', 'edit') %}disabled{% endif %}>
                        <option value="active" {{ (curDealer is not empty and curDealer.status is not empty and curDealer.status equals 'active') ? 'selected' : '' }}>Attivo</option>
                        <option value="inactive" {{ (curDealer is not empty and curDealer.status is not empty and curDealer.status equals 'inactive') ? 'selected' : '' }}>Inattivo</option>
                        <option value="pending" {{ (curDealer is not empty and curDealer.status is not empty and curDealer.status equals 'pending') ? 'selected' : '' }}>In Attesa</option>
                    </select>
                </div>

            </form>
        </div>
    </div>

    <!-- Sticky Footer -->
    <div class="flex-shrink-0 border-t border-gray-200 bg-white px-4 py-4 dark:border-neutral-700 dark:bg-neutral-900">
        <div class="flex justify-end gap-x-2">
            {% if curDealer is empty %}
            <!-- New Dealer - Show Save Button -->
            {% if user.hasPermission('DEALER_MANAGEMENT', 'create') %}
            <button type="submit" form="dealer-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                Salva Dealer
            </button>
            {% else %}
            <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                Non hai i permessi per creare dealer
            </div>
            {% endif %}
            {% else %}
            <!-- Edit Dealer - Show Update and Delete Buttons -->
            {% if user.hasPermission('DEALER_MANAGEMENT', 'delete') %}
            <button type="button" data-dealerid="{{ curDealer.id }}" id="delete-dealer-btn" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-red-600 hover:bg-red-50 focus:outline-none focus:bg-red-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-red-500 dark:hover:bg-red-800/30 dark:focus:bg-red-800/30">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 6h18"/>
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                    <line x1="10" x2="10" y1="11" y2="17"/>
                    <line x1="14" x2="14" y1="11" y2="17"/>
                </svg>
                Elimina
            </button>
            {% endif %}
            {% if user.hasPermission('DEALER_MANAGEMENT', 'edit') %}
            <button type="submit" form="dealer-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                Modifica Dealer
            </button>
            {% else %}
            <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                Non hai i permessi per modificare dealer
            </div>
            {% endif %}
            {% endif %}
        </div>
    </div>
</div>
#}