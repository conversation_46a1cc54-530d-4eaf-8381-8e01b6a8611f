<!-- Brand Form Content for Offcanvas -->
<script class="reload-script-on-load">
    addRoute('BE_BRAND', '{{ routes("BE_BRAND") }}');
    addRoute('BE_BRAND_SAVE', '{{ routes("BE_BRAND_SAVE") }}');
    addRoute('BE_BRAND_OPERATE', '{{ routes("BE_BRAND_OPERATE") }}');
</script>

<!-- Form Content with Sticky Footer -->
<div class="flex flex-col h-full">
    <!-- Scrollable Content Area -->
    <div class="flex-1 overflow-y-auto">
        <div class="p-4">
            {% set postUrl = routes('BE_BRAND_SAVE') %}
            {% if curBrand.id is not empty %}
            {% set postUrl = routes('BE_BRAND_SAVE') + '?brandId=' + curBrand.id %}
            {% endif %}

            <form id="brand-edit-offcanvas" method="POST" action="{{ postUrl }}" enctype="multipart/form-data" class="form-validate-jquery">
                <!-- Hidden ID field -->
                <input type="hidden" name="id" value="{{ curBrand.id }}">

                <!-- Description -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Descrizione: <span class="text-red-500">*</span>
                    </label>
                    <input name="descrizione" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Descrizione del marchio" value="{{ curBrand.descrizione }}" required maxlength="100" {% if not user.hasPermission('BRAND_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Code -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Codice: <span class="text-red-500">*</span>
                    </label>
                    <input name="codice" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Codice del marchio" value="{{ curBrand.codice }}" required maxlength="50" {% if not user.hasPermission('BRAND_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

            </form>
        </div>
    </div>

    <!-- Sticky Footer -->
    <div class="flex justify-end items-center gap-x-2 py-3 px-4 border-t dark:border-neutral-700">
        {% if user.hasPermission('BRAND_MANAGEMENT', 'delete') and curBrand.id is not empty %}
        <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-red-600 text-white hover:bg-red-700 focus:outline-hidden focus:bg-red-700 disabled:opacity-50 disabled:pointer-events-none" id="delete-brand-btn">
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"/><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/><line x1="10" y1="11" x2="10" y2="17"/><line x1="14" y1="11" x2="14" y2="17"/></svg>
            Elimina
        </button>
        {% endif %}
        
        <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-overlay="#brand-offcanvas">
            Annulla
        </button>
        
        {% if user.hasPermission('BRAND_MANAGEMENT', 'create') or user.hasPermission('BRAND_MANAGEMENT', 'edit') %}
        <button type="submit" form="brand-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-hidden focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/><polyline points="17 21 17 13 7 13 7 21"/><polyline points="7 3 7 8 15 8"/></svg>
            Salva
        </button>
        {% endif %}
    </div>
    <!-- End Sticky Footer -->
</div>
