<!-- Country Form Content for Offcanvas -->
<script class="reload-script-on-load">
    addRoute('BE_COUNTRY', '{{ routes("BE_COUNTRY") }}');
    addRoute('BE_COUNTRY_SAVE', '{{ routes("BE_COUNTRY_SAVE") }}');
    addRoute('BE_COUNTRY_OPERATE', '{{ routes("BE_COUNTRY_OPERATE") }}');
</script>

<!-- Form Content with Sticky Footer -->
<div class="flex flex-col h-full">
    <!-- Scrollable Content Area -->
    <div class="flex-1 overflow-y-auto">
        <div class="p-4">
            {% set postUrl = routes('BE_COUNTRY_SAVE') %}
            {% if curCountry.id is not empty %}                
            {% set postUrl = routes('BE_COUNTRY_SAVE') + '?countryId=' + curCountry.id %}
            {% endif %}

            <form id="country-edit-offcanvas" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                <!-- Description -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Descrizione: <span class="text-red-500">*</span>
                    </label>
                    <input name="description" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Descrizione del paese" value="{{ curCountry.description }}" required {% if not user.hasPermission('COUNTRY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Code -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Codice: <span class="text-red-500">*</span>
                    </label>
                    <input name="code" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Codice del paese (es. IT, FR, DE)" value="{{ curCountry.code }}" required {% if not user.hasPermission('COUNTRY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Codice Belfiore -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Codice Belfiore:
                    </label>
                    <input name="codiceBelfiore" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Codice Belfiore" value="{{ curCountry.codiceBelfiore }}" {% if not user.hasPermission('COUNTRY_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

            </form>
        </div>
    </div>

    <!-- Sticky Footer -->
    <div class="flex-shrink-0 border-t border-gray-200 bg-white px-4 py-4 dark:border-neutral-700 dark:bg-neutral-900">
        <div class="flex justify-end gap-x-2">
            {% if curCountry is empty %}
                <!-- New Country - Show Save Button -->
                {% if user.hasPermission('COUNTRY_MANAGEMENT', 'create') %}
                <button type="submit" form="country-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Salva Paese
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per creare paesi
                </div>
                {% endif %}
            {% else %}
                <!-- Edit Country - Show Update and Delete Buttons -->
                {% if user.hasPermission('COUNTRY_MANAGEMENT', 'delete') %}
                <button type="button" data-countryid="{{ curCountry.id }}" id="delete-country-btn" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-red-600 hover:bg-red-50 focus:outline-none focus:bg-red-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-red-500 dark:hover:bg-red-800/30 dark:focus:bg-red-800/30">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 6h18"/>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                        <line x1="10" x2="10" y1="11" y2="17"/>
                        <line x1="14" x2="14" y1="11" y2="17"/>
                    </svg>
                    Elimina
                </button>
                {% endif %}
                {% if user.hasPermission('COUNTRY_MANAGEMENT', 'edit') %}
                <button type="submit" form="country-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Modifica Paese
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per modificare paesi
                </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
</div>
