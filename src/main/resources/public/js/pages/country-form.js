// Country Form Management
const CountryForm = function () {

    // Form validation configuration
    const _componentValidation = function() {
        const form = document.getElementById('country-edit-offcanvas');
        if (!form) return;

        // Initialize form validation
        const validator = new FormValidator(form, {
            rules: {
                description: {
                    required: true,
                    minlength: 2,
                    maxlength: 100
                },
                code: {
                    required: true,
                    minlength: 2,
                    maxlength: 10,
                    pattern: /^[A-Z0-9]+$/
                },
                codiceBelfiore: {
                    maxlength: 10,
                    pattern: /^[A-Z0-9]*$/
                }
            },
            messages: {
                description: {
                    required: "La descrizione è obbligatoria",
                    minlength: "La descrizione deve essere di almeno 2 caratteri",
                    maxlength: "La descrizione non può superare i 100 caratteri"
                },
                code: {
                    required: "Il codice è obbligatorio",
                    minlength: "Il codice deve essere di almeno 2 caratteri",
                    maxlength: "Il codice non può superare i 10 caratteri",
                    pattern: "Il codice può contenere solo lettere maiuscole e numeri"
                },
                codiceBelfiore: {
                    maxlength: "Il codice Belfiore non può superare i 10 caratteri",
                    pattern: "Il codice Belfiore può contenere solo lettere maiuscole e numeri"
                }
            },
            errorClass: 'text-red-500 text-sm mt-1',
            errorElement: 'div',
            highlight: function(element) {
                element.classList.add('border-red-500');
                element.classList.remove('border-gray-200');
            },
            unhighlight: function(element) {
                element.classList.remove('border-red-500');
                element.classList.add('border-gray-200');
            }
        });

        return validator;
    };

    // Form submission handling
    const _componentFormSubmission = function() {
        const form = document.getElementById('country-edit-offcanvas');
        if (!form) return;

        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validate form
            const validator = _componentValidation();
            if (validator && !validator.form()) {
                showToast('Correggi gli errori nel form prima di continuare', 'error');
                return;
            }

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = `
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Salvataggio...
            `;

            // Prepare form data
            const formData = new FormData(form);

            // Submit form
            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    return response.text();
                }
                throw new Error('Network response was not ok');
            })
            .then(data => {
                // Success - close offcanvas and reload table
                const offcanvasElement = form.closest('.hs-overlay');
                if (offcanvasElement) {
                    const offcanvasInstance = HSOverlay.getInstance(offcanvasElement, true);
                    if (offcanvasInstance) {
                        offcanvasInstance.close();
                    }
                }

                // Trigger success callback if available
                if (window.currentOffcanvasConfig && window.currentOffcanvasConfig.onSuccess) {
                    window.currentOffcanvasConfig.onSuccess();
                }

                // Show success message
                const isEdit = form.action.includes('countryId=');
                const message = isEdit ? 'Paese modificato con successo' : 'Paese creato con successo';
                showToast(message, 'success');
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Errore durante il salvataggio', 'error');
            })
            .finally(() => {
                // Restore button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    };

    // Delete button handling
    const _componentDeleteButton = function() {
        const deleteBtn = document.getElementById('delete-country-btn');
        if (!deleteBtn) return;

        deleteBtn.addEventListener('click', function() {
            const countryId = this.getAttribute('data-countryid');
            if (!countryId) return;

            Swal.fire({
                title: 'Conferma eliminazione',
                text: 'Sei sicuro di voler eliminare questo paese?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Sì, elimina',
                cancelButtonText: 'Annulla'
            }).then((result) => {
                if (result.isConfirmed) {
                    CountryForm._performDelete(countryId);
                }
            });
        });
    };

    // Input formatting and validation
    const _componentInputFormatting = function() {
        // Code field - uppercase transformation
        const codeInput = document.querySelector('input[name="code"]');
        if (codeInput) {
            codeInput.addEventListener('input', function() {
                this.value = this.value.toUpperCase();
            });
        }

        // Codice Belfiore field - uppercase transformation
        const codiceBelfioreInput = document.querySelector('input[name="codiceBelfiore"]');
        if (codiceBelfioreInput) {
            codiceBelfioreInput.addEventListener('input', function() {
                this.value = this.value.toUpperCase();
            });
        }

        // Description field - trim whitespace
        const descriptionInput = document.querySelector('input[name="description"]');
        if (descriptionInput) {
            descriptionInput.addEventListener('blur', function() {
                this.value = this.value.trim();
            });
        }
    };

    // Public methods
    return {
        // Initialize all components
        init: function() {
            _componentValidation();
            _componentFormSubmission();
            _componentDeleteButton();
            _componentInputFormatting();
        },

        // Private helper methods
        _performDelete: function(countryId) {
            const formData = new FormData();
            formData.append('operation', 'delete');
            formData.append('countryIds', countryId);

            fetch(appRoutes.get("BE_COUNTRY_OPERATE"), {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    return response.text();
                }
                throw new Error('Network response was not ok');
            })
            .then(data => {
                // Close offcanvas
                const form = document.getElementById('country-edit-offcanvas');
                if (form) {
                    const offcanvasElement = form.closest('.hs-overlay');
                    if (offcanvasElement) {
                        const offcanvasInstance = HSOverlay.getInstance(offcanvasElement, true);
                        if (offcanvasInstance) {
                            offcanvasInstance.close();
                        }
                    }
                }

                // Trigger success callback if available
                if (window.currentOffcanvasConfig && window.currentOffcanvasConfig.onSuccess) {
                    window.currentOffcanvasConfig.onSuccess();
                }

                showToast('Paese eliminato con successo', 'success');
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Errore durante l\'eliminazione', 'error');
            });
        }
    };
}();

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    CountryForm.init();
});

// Also initialize when content is dynamically loaded (for offcanvas)
document.addEventListener('hs.overlay.open', function() {
    // Small delay to ensure content is loaded
    setTimeout(function() {
        CountryForm.init();
    }, 100);
});
