// Global variables for table management
const CountryCollection = function () {

    // Datatable
    const _componentDatatable = function () {
        // Set starting date
        var params = [];
        var startDate = moment().subtract(29, 'days').format('DD/MM/YYYY');
        var endDate = moment().format('DD/MM/YYYY');
        if (startDate && startDate.trim() !== '') {
            params.push("startDate=" + encodeURIComponent(startDate));
        }
        if (endDate && endDate.trim() !== '') {
            params.push("endDate=" + encodeURIComponent(endDate));
        }

        // Initialize Preline UI DataTable with JavaScript configuration
        const datatableConfig = {            
            paging: true,
            pageLength: 10,
            searching: true,
            ordering: true,
            info: true,
            lengthChange: true,
            scrollCollapse: true,            
            ajax: {
                url: appRoutes.get("BE_COUNTRY_DATA") + (params.length > 0 ? '?' + params.join('&') : ''),
                type: 'GET'
            },
            select: {
                style: 'multi',
                selector: 'td:select-checkbox'
            },
            responsive: {
                details: {
                    type: 'column',
                    target: -1,
                }
            },
            columnDefs: [                
                {
                    targets: '_all', 
                    className: 'p-3 whitespace-nowrap text-sm text-gray-800 dark:text-neutral-200',
                },
                {
                    targets: 0,
                    orderable: false,
                    className: '!py-1 px-5 w-0 select-checkbox',
                    render: function(data, type, row, meta) {
                        if (type === 'display') {
                            const rowId = data || meta.row;
                            return `
                                <div class="flex items-center h-5">
                                    <input id="hs-table-checkbox-${rowId}" type="checkbox" class="border-gray-300 rounded-sm text-blue-600 checked:border-blue-500 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" data-hs-datatable-row-selecting-individual="" data-country-id="${data}">
                                    <label for="hs-table-checkbox-${rowId}" class="sr-only">Checkbox</label>
                                </div>
                            `;
                        }
                        return data;
                    }
                },
                {
                    targets: -1,
                    orderable: false,   
                    className: 'w-0 text-center',
                    render: function(data, type, row, meta) {
                        if (type === 'display') {
                            return _renderActionDropdown(row);
                        }
                        return data;
                    }
                }
            ],
            language: {                
                lengthMenu: 'Mostra _MENU_ elementi',
                paginate: {
                    first: 'Primo',
                    last: 'Ultimo',
                    next: 'Successivo',
                    previous: 'Precedente'
                },
                info: 'Mostra da _START_ a _END_ di _TOTAL_ elementi',
                infoEmpty: 'Mostra 0 a 0 di 0 elementi',
                infoFiltered: '(filtrati da _MAX_ elementi totali)',                
                emptyTable: '<div class=\"p-5 h-full flex flex-col justify-center items-center text-center\"><svg class=\"w-48 mx-auto mb-4\" width=\"178\" height=\"90\" viewBox=\"0 0 178 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-50 dark:stroke-neutral-700/10\"/><rect x=\"34.5\" y=\"58\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"61\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"73\" width=\"77\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/30\"/><rect x=\"27\" y=\"36\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"39\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"51\" width=\"92\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><g filter=\"url(#@@id)\"><rect x=\"12\" y=\"6\" width=\"154\" height=\"40\" rx=\"8\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\" shape-rendering=\"crispEdges\"/><rect x=\"12.5\" y=\"6.5\" width=\"153\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/60\" shape-rendering=\"crispEdges\"/><rect x=\"20\" y=\"14\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700 \"/><rect x=\"52\" y=\"17\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/><rect x=\"52\" y=\"29\" width=\"106\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/></g><defs><filter id=\"@@id\" x=\"0\" y=\"0\" width=\"178\" height=\"64\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\"><feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/><feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\"/><feOffset dy=\"6\"/><feGaussianBlur stdDeviation=\"6\"/><feComposite in2=\"hardAlpha\" operator=\"out\"/><feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0\"/><feBlend mode=\"normal\" in2=\"BackgroundImageFix\" result=\"effect1_dropShadow_1187_14810\"/><feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"effect1_dropShadow_1187_14810\" result=\"shape\"/></filter></defs></svg><div class=\"max-w-sm mx-auto\"><p class=\"mt-2 text-sm text-gray-600 dark:text-neutral-400\">Nessun dato disponibile</p></div></div>',
                zeroRecords: '<div class=\"p-5 h-full flex flex-col justify-center items-center text-center\"><svg class=\"w-48 mx-auto mb-4\" width=\"178\" height=\"90\" viewBox=\"0 0 178 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-50 dark:stroke-neutral-700/10\"/><rect x=\"34.5\" y=\"58\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"61\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"73\" width=\"77\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/30\"/><rect x=\"27\" y=\"36\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"39\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"51\" width=\"92\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><g filter=\"url(#@@id)\"><rect x=\"12\" y=\"6\" width=\"154\" height=\"40\" rx=\"8\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\" shape-rendering=\"crispEdges\"/><rect x=\"12.5\" y=\"6.5\" width=\"153\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/60\" shape-rendering=\"crispEdges\"/><rect x=\"20\" y=\"14\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700 \"/><rect x=\"52\" y=\"17\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/><rect x=\"52\" y=\"29\" width=\"106\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/></g><defs><filter id=\"@@id\" x=\"0\" y=\"0\" width=\"178\" height=\"64\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\"><feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/><feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\"/><feOffset dy=\"6\"/><feGaussianBlur stdDeviation=\"6\"/><feComposite in2=\"hardAlpha\" operator=\"out\"/><feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0\"/><feBlend mode=\"normal\" in2=\"BackgroundImageFix\" result=\"effect1_dropShadow_1187_14810\"/><feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"effect1_dropShadow_1187_14810\" result=\"shape\"/></filter></defs></svg><div class=\"max-w-sm mx-auto\"><p class=\"mt-2 text-sm text-gray-600 dark:text-neutral-400\">Nessun dato disponibile</p></div></div>',
                loadingRecords: '<div class=\"p-5 h-full flex flex-col justify-center items-center text-center\"><svg class=\"w-48 mx-auto mb-4\" width=\"178\" height=\"90\" viewBox=\"0 0 178 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-50 dark:stroke-neutral-700/10\"/><rect x=\"34.5\" y=\"58\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"61\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"73\" width=\"77\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/30\"/><rect x=\"27\" y=\"36\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"39\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"51\" width=\"92\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><g filter=\"url(#@@id)\"><rect x=\"12\" y=\"6\" width=\"154\" height=\"40\" rx=\"8\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\" shape-rendering=\"crispEdges\"/><rect x=\"12.5\" y=\"6.5\" width=\"153\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/60\" shape-rendering=\"crispEdges\"/><rect x=\"20\" y=\"14\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700 \"/><rect x=\"52\" y=\"17\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/><rect x=\"52\" y=\"29\" width=\"106\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/></g><defs><filter id=\"@@id\" x=\"0\" y=\"0\" width=\"178\" height=\"64\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\"><feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/><feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\"/><feOffset dy=\"6\"/><feGaussianBlur stdDeviation=\"6\"/><feComposite in2=\"hardAlpha\" operator=\"out\"/><feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0\"/><feBlend mode=\"normal\" in2=\"BackgroundImageFix\" result=\"effect1_dropShadow_1187_14810\"/><feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"effect1_dropShadow_1187_14810\" result=\"shape\"/></filter></defs></svg><div class=\"max-w-sm mx-auto\"><p class=\"mt-2 text-sm text-gray-600 dark:text-neutral-400\">Caricamento</p></div></div>',
                processing: '<div class=\"p-5 h-full flex flex-col justify-center items-center text-center\"><svg class=\"w-48 mx-auto mb-4\" width=\"178\" height=\"90\" viewBox=\"0 0 178 90\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"27\" y=\"50.5\" width=\"124\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-50 dark:stroke-neutral-700/10\"/><rect x=\"34.5\" y=\"58\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"61\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"66.5\" y=\"73\" width=\"77\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-50 dark:fill-neutral-700/30\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\"/><rect x=\"19.5\" y=\"28.5\" width=\"139\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/30\"/><rect x=\"27\" y=\"36\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"39\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><rect x=\"59\" y=\"51\" width=\"92\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-100 dark:fill-neutral-700/70\"/><g filter=\"url(#@@id)\"><rect x=\"12\" y=\"6\" width=\"154\" height=\"40\" rx=\"8\" fill=\"currentColor\" class=\"fill-white dark:fill-neutral-800\" shape-rendering=\"crispEdges\"/><rect x=\"12.5\" y=\"6.5\" width=\"153\" height=\"39\" rx=\"7.5\" stroke=\"currentColor\" class=\"stroke-gray-100 dark:stroke-neutral-700/60\" shape-rendering=\"crispEdges\"/><rect x=\"20\" y=\"14\" width=\"24\" height=\"24\" rx=\"4\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700 \"/><rect x=\"52\" y=\"17\" width=\"60\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/><rect x=\"52\" y=\"29\" width=\"106\" height=\"6\" rx=\"3\" fill=\"currentColor\" class=\"fill-gray-200 dark:fill-neutral-700\"/></g><defs><filter id=\"@@id\" x=\"0\" y=\"0\" width=\"178\" height=\"64\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\"><feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/><feColorMatrix in=\"SourceAlpha\" type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\" result=\"hardAlpha\"/><feOffset dy=\"6\"/><feGaussianBlur stdDeviation=\"6\"/><feComposite in2=\"hardAlpha\" operator=\"out\"/><feColorMatrix type=\"matrix\" values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0\"/><feBlend mode=\"normal\" in2=\"BackgroundImageFix\" result=\"effect1_dropShadow_1187_14810\"/><feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"effect1_dropShadow_1187_14810\" result=\"shape\"/></filter></defs></svg><div class=\"max-w-sm mx-auto\"><p class=\"mt-2 text-sm text-gray-600 dark:text-neutral-400\">Elaborazione</p></div></div>'
            },
            pagingOptions: {
                pageBtnClasses: 'min-w-10 flex justify-center items-center text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 py-2.5 text-sm rounded-full disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:focus:bg-neutral-700 dark:hover:bg-neutral-700'
            },
            selecting: true,
            rowSelectingOptions: {
                selectAllSelector: '#hs-table-search-checkbox-all'
            },
            layout: {
                topStart: {
                    buttons: ["copy", "csv", "excel", "pdf", "print"]
                }
            },
            order: [[1, 'asc']]
        };

        const tableEl = document.getElementById('country-datatable-container');
        const hsDataTable = new HSDataTable(tableEl, datatableConfig);

        // On draw initialize HS components and setup checkbox handlers
        hsDataTable.dataTable.on('draw', function() {
            HSStaticMethods.autoInit();
            _setupCheckboxHandlers();
        });

        window.countriesDataTable = hsDataTable;

        const buttons = document.querySelectorAll('#hs-dropdown-datatable-with-export .hs-dropdown-menu button');
        buttons.forEach((btn) => {
            const type = btn.getAttribute('data-hs-datatable-action-type');

            btn.addEventListener('click', () => hsDataTable.dataTable.button(`.buttons-${type}`).trigger());
        });

        // Initial setup of checkbox handlers
        _setupCheckboxHandlers();
    };

    // Helper function to render action dropdown
    const _renderActionDropdown = function(row) {
        const countryId = row[0] || '';
        return `
            <div class="hs-dropdown relative inline-flex">
                <button id="hs-table-dropdown-${countryId}" type="button" class="hs-dropdown-toggle py-1.5 px-2 inline-flex justify-center items-center gap-2 rounded-lg text-gray-700 align-middle disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-blue-600 transition-all text-sm dark:text-gray-400 dark:hover:text-white dark:focus:ring-offset-gray-800" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                    <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="1"/><circle cx="19" cy="12" r="1"/><circle cx="5" cy="12" r="1"/></svg>
                </button>
                <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden divide-y divide-gray-200 min-w-40 z-20 bg-white shadow-2xl rounded-lg p-2 mt-2 dark:divide-gray-700 dark:bg-gray-800 dark:border dark:border-gray-700" role="menu" aria-orientation="vertical" aria-labelledby="hs-table-dropdown-${countryId}">
                    <div class="py-2 first:pt-0 last:pb-0">
                        <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300 cursor-pointer" onclick="CountryCollection.editCountry('${countryId}'); return false;">
                            <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/><path d="m15 5 4 4"/></svg>
                            Modifica
                        </a>
                        <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-red-600 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500 dark:text-red-500 dark:hover:bg-gray-700 cursor-pointer" onclick="CountryCollection.deleteCountry('${countryId}'); return false;">
                            <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>
                            Elimina
                        </a>
                    </div>
                </div>
            </div>
        `;
    };

    // Setup checkbox handlers for bulk operations
    const _setupCheckboxHandlers = function() {
        const checkboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');
        const selectAllCheckbox = document.getElementById('hs-table-search-checkbox-all');
        const bulkActionsContainer = document.getElementById('bulk-actions-container');
        const selectedCountElement = document.getElementById('selected-count');

        // Update bulk actions visibility and count
        const updateBulkActions = function() {
            const checkedBoxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');
            const count = checkedBoxes.length;

            if (selectedCountElement) {
                selectedCountElement.textContent = count;
            }

            if (bulkActionsContainer) {
                if (count > 0) {
                    bulkActionsContainer.classList.remove('hidden');
                } else {
                    bulkActionsContainer.classList.add('hidden');
                }
            }
        };

        // Individual checkbox change handler
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateBulkActions();

                // Update select all checkbox state
                if (selectAllCheckbox) {
                    const totalCheckboxes = checkboxes.length;
                    const checkedCheckboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked').length;

                    if (checkedCheckboxes === 0) {
                        selectAllCheckbox.indeterminate = false;
                        selectAllCheckbox.checked = false;
                    } else if (checkedCheckboxes === totalCheckboxes) {
                        selectAllCheckbox.indeterminate = false;
                        selectAllCheckbox.checked = true;
                    } else {
                        selectAllCheckbox.indeterminate = true;
                        selectAllCheckbox.checked = false;
                    }
                }
            });
        });

        // Select all checkbox handler
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                checkboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
                updateBulkActions();
            });
        }

        // Initial update
        updateBulkActions();
    };

    // Date range picker initialization
    const _componentDateRangePicker = function() {
        const dateRangeInput = document.getElementById('daterange-input');
        if (!dateRangeInput) return;

        const picker = new HSDatepicker(dateRangeInput, {
            mode: 'range',
            dateFormat: 'DD/MM/YYYY',
            locale: 'it',
            onSelect: function(dates) {
                if (dates && dates.length === 2) {
                    const startDate = moment(dates[0]).format('DD/MM/YYYY');
                    const endDate = moment(dates[1]).format('DD/MM/YYYY');

                    // Reload table with new date range
                    if (window.countriesDataTable) {
                        const newUrl = appRoutes.get("BE_COUNTRY_DATA") +
                            '?startDate=' + encodeURIComponent(startDate) +
                            '&endDate=' + encodeURIComponent(endDate);
                        window.countriesDataTable.dataTable.ajax.url(newUrl).load();
                    }
                }
            }
        });
    };

    // Public methods
    return {
        // Initialize all components
        init: function() {
            _componentDatatable();
            _componentDateRangePicker();

            // Setup event handlers
            const createBtn = document.getElementById('create-country-btn');
            if (createBtn) {
                createBtn.addEventListener('click', function() {
                    CountryCollection.createCountry();
                });
            }

            // Setup table row click handlers
            document.addEventListener('click', function(e) {
                const link = e.target.closest('a[countryId]');
                if (link) {
                    e.preventDefault();
                    const countryId = link.getAttribute('countryId');
                    CountryCollection.editCountry(countryId);
                }
            });
        },

        // Create new country
        createCountry: function() {
            const url = appRoutes.get("BE_COUNTRY_FORM");
            createDynamicOffcanvas({
                url: url,
                title: 'Nuovo Paese',
                size: 'md',
                onSuccess: function() {
                    CountryCollection.reloadTable();
                    showToast('Paese creato con successo', 'success');
                }
            });
        },

        // Edit existing country
        editCountry: function(countryId) {
            if (!countryId) return;

            const url = appRoutes.get("BE_COUNTRY_FORM") + '?countryId=' + encodeURIComponent(countryId);
            createDynamicOffcanvas({
                url: url,
                title: 'Modifica Paese',
                size: 'md',
                tabs: [
                    { id: 'details', title: 'Dettaglio', active: true },
                    { id: 'activity', title: 'Attività' }
                ],
                onSuccess: function() {
                    CountryCollection.reloadTable();
                    showToast('Paese modificato con successo', 'success');
                },
                onTabChange: function(tabId) {
                    if (tabId === 'activity') {
                        loadEntityLog('Country', countryId, 'activity');
                    }
                }
            });
        },

        // Delete single country
        deleteCountry: function(countryId) {
            if (!countryId) return;

            Swal.fire({
                title: 'Conferma eliminazione',
                text: 'Sei sicuro di voler eliminare questo paese?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Sì, elimina',
                cancelButtonText: 'Annulla'
            }).then((result) => {
                if (result.isConfirmed) {
                    CountryCollection._performBulkOperation('delete', [countryId]);
                }
            });
        },

        // Archive selected rows
        archiveSelectedRows: function() {
            const selectedIds = CountryCollection._getSelectedRowIds();
            if (selectedIds.length === 0) {
                showToast('Seleziona almeno un paese', 'warning');
                return;
            }

            Swal.fire({
                title: 'Conferma archiviazione',
                text: `Sei sicuro di voler archiviare ${selectedIds.length} paese/i?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Sì, archivia',
                cancelButtonText: 'Annulla'
            }).then((result) => {
                if (result.isConfirmed) {
                    CountryCollection._performBulkOperation('archive', selectedIds);
                }
            });
        },

        // Delete selected rows
        deleteSelectedRows: function() {
            const selectedIds = CountryCollection._getSelectedRowIds();
            if (selectedIds.length === 0) {
                showToast('Seleziona almeno un paese', 'warning');
                return;
            }

            Swal.fire({
                title: 'Conferma eliminazione',
                text: `Sei sicuro di voler eliminare ${selectedIds.length} paese/i?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Sì, elimina',
                cancelButtonText: 'Annulla'
            }).then((result) => {
                if (result.isConfirmed) {
                    CountryCollection._performBulkOperation('delete', selectedIds);
                }
            });
        },

        // Clear selection
        clearSelection: function() {
            const checkboxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]');
            const selectAllCheckbox = document.getElementById('hs-table-search-checkbox-all');

            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });

            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            }

            const bulkActionsContainer = document.getElementById('bulk-actions-container');
            if (bulkActionsContainer) {
                bulkActionsContainer.classList.add('hidden');
            }
        },

        // Reload table data
        reloadTable: function() {
            if (window.countriesDataTable) {
                window.countriesDataTable.dataTable.ajax.reload();
            }
        },

        // Private helper methods
        _getSelectedRowIds: function() {
            const selectedIds = [];
            const checkedBoxes = document.querySelectorAll('[data-hs-datatable-row-selecting-individual]:checked');

            checkedBoxes.forEach(checkbox => {
                const countryId = checkbox.getAttribute('data-country-id');
                if (countryId) {
                    selectedIds.push(countryId);
                }
            });

            return selectedIds;
        },

        _performBulkOperation: function(operation, countryIds) {
            if (!countryIds || countryIds.length === 0) return;

            const formData = new FormData();
            formData.append('operation', operation);
            formData.append('countryIds', countryIds.join(','));

            fetch(appRoutes.get("BE_COUNTRY_OPERATE"), {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    return response.text();
                }
                throw new Error('Network response was not ok');
            })
            .then(data => {
                CountryCollection.reloadTable();
                CountryCollection.clearSelection();

                let message = '';
                switch(operation) {
                    case 'delete':
                        message = `${countryIds.length} paese/i eliminato/i con successo`;
                        break;
                    case 'archive':
                        message = `${countryIds.length} paese/i archiviato/i con successo`;
                        break;
                    case 'unarchive':
                        message = `${countryIds.length} paese/i ripristinato/i con successo`;
                        break;
                    default:
                        message = 'Operazione completata con successo';
                }

                showToast(message, 'success');
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Errore durante l\'operazione', 'error');
            });
        }
    };
}();

// Table Collection utility for archived filter
const TableCollection = {
    reloadTable: function(showArchived = false) {
        if (window.countriesDataTable) {
            let url = appRoutes.get("BE_COUNTRY_DATA");
            if (showArchived) {
                url += '?archived=true';
            }
            window.countriesDataTable.dataTable.ajax.url(url).load();
        }
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    CountryCollection.init();
});
